import argparse
import logging
import pathlib
import time
import pandas as pd
import glob

import gin

# from bounce.bounce import Bounce
from bounce_ga.bounce_ga import Bounce
from bounce_ga.group import select_best_group
from bounce.util.data_loading import (
    download_uci_data,
    download_maxsat60_data,
    download_maxsat125_data,
)
from bounce.util.printing import BColors, BOUNCE_NAME


if __name__ == "__main__":

    logging.basicConfig(
        level=logging.INFO,
        format=f"{BColors.LIGHTGREY} %(levelname)s:%(asctime)s - (%(filename)s:%(lineno)d) - %(message)s {BColors.ENDC}",
    )

    logging.info(BOUNCE_NAME)

    if not pathlib.Path("data/slice_localization_data.csv").exists():
        download_uci_data()

    if not pathlib.Path("data/maxsat/frb10-6-4.wcnf").exists():
        download_maxsat60_data()

    if not pathlib.Path(
        "data/maxsat/cluster-expansion-IS1_5.*******.5_softer_periodic.wcnf"
    ).exists():
        download_maxsat125_data()

    then = time.time()
    parser = argparse.ArgumentParser(
        prog=BOUNCE_NAME,
        description="Bounce: Reliable High-Dimensional Bayesian Optimization Algorithm for Combinatorial and Mixed Spaces",
        epilog="For more information, please contact the author.",
    )

    parser.add_argument(
        "--gin-files",
        type=str,
        nargs="+",
        default=["configs/rbf_model.gin"],
        help="Path to the config file",
    )
    parser.add_argument(
        "--gin-bindings",
        type=str,
        nargs="+",
        default=[],
    )

    parser.add_argument(
        "--n-repeat",
        type=int,
        default=14,
        help="实验重复次数"
    )

    args = parser.parse_args()

    for i in range(args.n_repeat):
        logging.info(f"第{i+1}次实验开始")
        gin.parse_config_files_and_bindings(args.gin_files, args.gin_bindings)
        bounce1 = Bounce()
        # bounce2 = Bounce()
        # bounce_best = select_best_group(bounce1 , bounce2)
        bounce1.run()
        gin.clear_config()
        logging.info(f"第{i+1}次实验结束")

    now = time.time()
    logging.info(f"Total time: {now - then:.2f} seconds")
    
    result_files = sorted(glob.glob("results/Labs/*/*/eval_history.csv"), key=lambda x: pathlib.Path(x).stat().st_mtime)
    all_histories = []
    all_steps = []

    for f in result_files:
        df = pd.read_csv(f)
        all_histories.append(df["当前最优值"].values)
        all_steps.append(df["评估次数"].values)

    max_len = max(len(h) for h in all_histories)
    aligned = []
    for h in all_histories:
        if len(h) < max_len:
            h = list(h) + [h[-1]] * (max_len - len(h))
        aligned.append(h)
    aligned = pd.DataFrame(aligned).T
    aligned.columns = [f"实验{i+1}" for i in range(len(result_files))]
    step_col = all_steps[all_histories.index(max(all_histories, key=len))]
    aligned.insert(0, "评估次数", step_col)
    aligned["均值"] = aligned.iloc[:, 1:].mean(axis=1)
    aligned.to_csv("eval_history.csv", index=False, float_format="%.8f", encoding="utf-8-sig")
    print(f"合并完成，共{len(result_files)}个实验，结果已保存到eval_history.csv")
