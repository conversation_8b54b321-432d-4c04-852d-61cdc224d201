# 传统GP模型配置文件
# 使用传统的高斯过程模型作为基线

# 导入默认配置
include 'bounce_ga/default.gin'

# 代理模型配置 (使用默认的GP模型设置)
create_candidates_discrete.use_rbf_model = False
create_candidates_discrete.use_virtual_points = False

# 遗传算法参数
create_candidates_discrete.population_size = 100
create_candidates_discrete.n_generations = 20

# 优化器配置 (覆盖默认值)
Bounce.maximum_number_evaluations = 50  # 减少评估次数用于快速测试
Bounce.device = "cpu"  # 使用CPU而不是CUDA
