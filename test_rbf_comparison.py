#!/usr/bin/env python3
"""
测试脚本：对比GP模型和RBF模型在混合变量优化中的表现
"""

import logging
import time
import torch
import numpy as np
from bounce.bounce import Bounce
from bounce.benchmarks import get_benchmark
import gin

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def run_experiment_with_model(benchmark_name: str, use_rbf: bool, use_virtual: bool, n_runs: int = 3):
    """
    运行实验对比不同代理模型
    
    Args:
        benchmark_name: 基准测试名称
        use_rbf: 是否使用RBF模型
        use_virtual: 是否使用虚拟点
        n_runs: 运行次数
        
    Returns:
        results: 实验结果列表
    """
    model_name = "RBF" if use_rbf else "GP"
    virtual_name = "with_virtual" if use_virtual else "no_virtual"
    
    logging.info(f"🚀 开始实验: {model_name} + {virtual_name} on {benchmark_name}")
    
    results = []
    
    for run in range(n_runs):
        logging.info(f"📊 运行 {run + 1}/{n_runs}")
        
        # 清除gin配置
        gin.clear_config()
        
        # 配置参数
        gin.parse_config([
            f"create_candidates_discrete.use_rbf_model = {use_rbf}",
            f"create_candidates_discrete.use_virtual_points = {use_virtual}",
            f"create_candidates_discrete.n_virtual_points = 20",
            "create_candidates_discrete.population_size = 100",
            "create_candidates_discrete.n_generations = 20",
            "Bounce.maximum_number_evaluations = 200",
            "Bounce.n_initial_points = 5",
        ])
        
        # 创建基准测试
        benchmark = get_benchmark(benchmark_name)
        
        # 创建优化器
        bounce = Bounce(
            benchmark=benchmark,
            device="cpu",
            maximum_number_evaluations=200,
            n_initial_points=5,
        )
        
        # 运行优化
        start_time = time.time()
        try:
            bounce.run()
            end_time = time.time()
            
            # 记录结果
            best_value = bounce.fx_tr.min().item()
            n_evals = len(bounce.fx_tr)
            runtime = end_time - start_time
            
            result = {
                'run': run + 1,
                'model': model_name,
                'virtual': virtual_name,
                'best_value': best_value,
                'n_evaluations': n_evals,
                'runtime': runtime,
                'success': True
            }
            
            logging.info(f"✅ 运行 {run + 1} 完成: 最优值 = {best_value:.4f}, 评估次数 = {n_evals}, 时间 = {runtime:.2f}s")
            
        except Exception as e:
            logging.error(f"❌ 运行 {run + 1} 失败: {e}")
            result = {
                'run': run + 1,
                'model': model_name,
                'virtual': virtual_name,
                'best_value': float('inf'),
                'n_evaluations': 0,
                'runtime': 0,
                'success': False,
                'error': str(e)
            }
        
        results.append(result)
    
    return results

def compare_models():
    """对比不同模型的性能"""
    
    # 测试基准
    benchmark_name = "Ackley-10"  # 可以改为其他基准测试
    
    # 测试配置
    configs = [
        {"use_rbf": False, "use_virtual": False, "name": "GP_baseline"},
        {"use_rbf": True, "use_virtual": False, "name": "RBF_baseline"},
        {"use_rbf": False, "use_virtual": True, "name": "GP_with_virtual"},
        {"use_rbf": True, "use_virtual": True, "name": "RBF_with_virtual"},
    ]
    
    all_results = []
    
    for config in configs:
        logging.info(f"\n{'='*60}")
        logging.info(f"🧪 测试配置: {config['name']}")
        logging.info(f"{'='*60}")
        
        results = run_experiment_with_model(
            benchmark_name=benchmark_name,
            use_rbf=config["use_rbf"],
            use_virtual=config["use_virtual"],
            n_runs=3
        )
        
        # 添加配置信息
        for result in results:
            result['config'] = config['name']
        
        all_results.extend(results)
    
    # 分析结果
    analyze_results(all_results)
    
    return all_results

def analyze_results(results):
    """分析实验结果"""
    
    logging.info(f"\n{'='*60}")
    logging.info("📈 实验结果分析")
    logging.info(f"{'='*60}")
    
    # 按配置分组
    configs = {}
    for result in results:
        if result['success']:
            config_name = result['config']
            if config_name not in configs:
                configs[config_name] = []
            configs[config_name].append(result)
    
    # 计算统计信息
    for config_name, config_results in configs.items():
        if not config_results:
            continue
            
        best_values = [r['best_value'] for r in config_results]
        runtimes = [r['runtime'] for r in config_results]
        n_evals = [r['n_evaluations'] for r in config_results]
        
        logging.info(f"\n🔍 {config_name}:")
        logging.info(f"  最优值: {np.mean(best_values):.4f} ± {np.std(best_values):.4f}")
        logging.info(f"  运行时间: {np.mean(runtimes):.2f} ± {np.std(runtimes):.2f} 秒")
        logging.info(f"  评估次数: {np.mean(n_evals):.1f} ± {np.std(n_evals):.1f}")
        logging.info(f"  成功率: {len(config_results)}/3")
    
    # 找出最佳配置
    best_config = None
    best_mean_value = float('inf')
    
    for config_name, config_results in configs.items():
        if config_results:
            mean_value = np.mean([r['best_value'] for r in config_results])
            if mean_value < best_mean_value:
                best_mean_value = mean_value
                best_config = config_name
    
    if best_config:
        logging.info(f"\n🏆 最佳配置: {best_config} (平均最优值: {best_mean_value:.4f})")

if __name__ == "__main__":
    logging.info("🔬 开始代理模型对比实验")
    
    try:
        results = compare_models()
        logging.info("\n✅ 实验完成!")
        
        # 保存结果到文件
        import json
        with open("rbf_comparison_results.json", "w") as f:
            json.dump(results, f, indent=2)
        logging.info("📁 结果已保存到 rbf_comparison_results.json")
        
    except Exception as e:
        logging.error(f"❌ 实验失败: {e}")
        raise
